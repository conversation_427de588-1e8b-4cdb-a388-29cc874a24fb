#!/bin/bash

echo "Applying quick fixes for compilation issues..."

# 1. Fix the version include issue by adding it at the very top
sed -i '1i\
#include <linux/version.h>\
#ifndef KERNEL_VERSION\
#define KERNEL_VERSION(a,b,c) (((a) << 16) + ((b) << 8) + (c))\
#endif' modules/GPL/oscompat.h

# 2. Fix the asm/system.h issue
sed -i 's|#include <asm/system.h>|#if LINUX_VERSION_CODE < KERNEL_VERSION(3,4,0)\
#include <asm/system.h>\
#else\
#include <asm/switch_to.h>\
#include <asm/barrier.h>\
#endif|' modules/GPL/oscompat.h

echo "Quick fixes applied. Try building again."
