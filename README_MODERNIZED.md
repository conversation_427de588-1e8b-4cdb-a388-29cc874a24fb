# Modernized HSF Modem Driver for Linux

This is a modernized version of the Conexant HSF (Hardware Specific Functions) softmodem driver, updated to work with modern Linux kernels including 5.x and 6.x series.

## Original Driver Information

- **Original Version**: 7.80.02.06full
- **Original Target**: Linux kernels 2.4-2.6
- **Manufacturer**: Conexant (formerly Rockwell)
- **Type**: HSF Softmodem family

## Modernization Changes

This driver has been updated to support modern Linux kernels with the following changes:

### Kernel Compatibility
- **Supported Kernels**: 2.6.x through 6.x (tested on 6.1.0-37-amd64)
- Updated kernel version checks to accept modern kernels
- Added compatibility macros for modern kernel APIs

### API Updates
- Removed deprecated `__devinit` and `__devexit` annotations
- Updated PCI slot name usage (`PCI_SLOT_NAME` → `pci_name`)
- Added modern interrupt handling compatibility
- Updated compiler detection for GCC 4-12
- Added x86_64 architecture support alongside i386

### Build System
- Enhanced Makefile with modern kernel feature detection
- Updated architecture detection for x86_64 systems
- Improved compiler version detection
- Added compatibility flags for modern kernels

## System Requirements

### Debian 12 (Bookworm)
- Kernel: 6.1.0-37-amd64 or compatible
- Architecture: x86_64 (with i386 compatibility)
- Required packages:
  ```bash
  sudo apt-get update
  sudo apt-get install build-essential linux-headers-$(uname -r)
  ```

### Hardware Support
The driver supports the same hardware as the original:
- HSF/HSFi modems (Standard and SmartDAA)
- HDA (High Definition Audio) modems
- Various PCI and USB modem devices
- See original README for complete device list

## Installation Instructions

### 1. Prepare the System
```bash
# Install required packages
sudo apt-get update
sudo apt-get install build-essential linux-headers-$(uname -r)

# Verify kernel headers
ls /lib/modules/$(uname -r)/build
```

### 2. Test Compilation
```bash
# Run the test script to verify everything compiles
./test_compile.sh
```

### 3. Install the Driver
```bash
# Install the driver
sudo make install

# Configure the modem
sudo hsfconfig
```

### 4. Load the Modules
```bash
# The hsfconfig script should load modules automatically
# You can also load them manually:
sudo modprobe hsfosspec
sudo modprobe hsfserial
sudo modprobe hsfengine
# ... other modules as needed
```

## Troubleshooting

### Compilation Issues
1. **Missing kernel headers**: Install with `sudo apt-get install linux-headers-$(uname -r)`
2. **GCC version mismatch**: The script will try to find a compatible GCC version
3. **Architecture mismatch**: The driver will warn but should work on x86_64 with i386 binaries

### Runtime Issues
1. **Module loading fails**: Check `dmesg` for error messages
2. **Device not detected**: Verify your modem is supported (see device list in README)
3. **Permission issues**: Make sure you're running installation as root

### Common Error Messages
- **"Unknown symbol"**: Usually indicates missing dependencies between modules
- **"Invalid module format"**: Kernel version mismatch, recompile for your kernel
- **"Device busy"**: Another driver may be using the device

## Testing the Installation

After installation, test the modem:

```bash
# Check if modules are loaded
lsmod | grep hsf

# Check for modem device
ls -la /dev/ttyS*

# Test with minicom or similar terminal program
sudo minicom -D /dev/ttyS0  # or appropriate device
```

## Known Limitations

1. **Architecture**: Primarily designed for i386, works on x86_64 with compatibility
2. **Modern kernels**: Some very new kernel features may not be supported
3. **Hardware**: Only supports Conexant-based modems

## Support and Reporting Issues

This is a community modernization effort. If you encounter issues:

1. Check the compilation log: `cat compile.log`
2. Check kernel messages: `dmesg | tail -50`
3. Verify your hardware is supported
4. Try the original troubleshooting steps from the original documentation

## License

This driver maintains the same licensing as the original:
- GPL for files in the "GPL" directory
- Proprietary license for other components (see LICENSE file)

## Credits

- Original driver: Conexant/Linuxant
- Modernization: Community effort for Debian 12 compatibility
