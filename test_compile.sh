#!/bin/bash

# Test compilation script for modernized HSF modem driver
# This script tests if the driver can compile on modern kernels

echo "HSF Modem Driver Modernization Test"
echo "===================================="

# Check kernel version
KERNEL_VERSION=$(uname -r)
echo "Current kernel: $KERNEL_VERSION"

# Check if we have kernel headers
KERNEL_HEADERS="/lib/modules/$KERNEL_VERSION/build"
if [ ! -d "$KERNEL_HEADERS" ]; then
    echo "ERROR: Kernel headers not found at $KERNEL_HEADERS"
    echo "Please install kernel headers with:"
    echo "  sudo apt-get install linux-headers-$(uname -r)"
    exit 1
fi

echo "Kernel headers found: $KERNEL_HEADERS"

# Check for required build tools
echo "Checking build tools..."
for tool in gcc make; do
    if ! which $tool >/dev/null 2>&1; then
        echo "ERROR: $tool not found. Please install build-essential:"
        echo "  sudo apt-get install build-essential"
        exit 1
    fi
done

echo "Build tools OK"

# Test compilation
echo "Testing module compilation..."
cd modules

# Clean first
make clean >/dev/null 2>&1

# Try to compile
echo "Attempting to compile modules..."
if make all 2>&1 | tee ../compile.log; then
    echo "SUCCESS: Modules compiled successfully!"
    echo "Generated modules:"
    ls -la *.ko 2>/dev/null || echo "No .ko files found"
else
    echo "COMPILATION FAILED. Check compile.log for details."
    echo "Last 20 lines of compilation output:"
    tail -20 ../compile.log
    exit 1
fi

echo ""
echo "Compilation test completed successfully!"
echo "You can now try to install the driver with 'make install'"
