#!/usr/bin/make -f

package=@CNXTTARGET@modem

-include debian/target.mak

build:
	$(checkdir)
	
	$(MAKE) all
	touch build

clean:
	$(checkdir)
	-rm -f build
	-$(MAKE) clean
	-rm -f `find . -name "*~"`
	-rm -rf debian/tmp debian/files* core debian/substvars

binary-indep: checkroot build
	$(checkdir)

binary-arch: checkroot build
	$(checkdir)
	-rm -rf debian/tmp
	install -d debian/tmp
ifneq ($(TARGET_KERNEL),)
	@MODS_DIR=binaries/linux-$(TARGET_KERNEL) ; \
	case "$(TARGET_KERNEL)" in \
	*[Ss][Mm][Pp]*) SMPSUFFIX="" ;; \
	*) SMPSUFFIX="-SMP" ;; \
	esac ; \
	if [ "$(TARGET_DISTRO)" = "custom" ]; then \
		KERNELSRC="/lib/modules/`uname -r`/build"; \
	else \
		KERNELSRC="$(DISTROKERNHDRSDIR)/$(TARGET_DISTRO)/linux-$(TARGET_KERNEL)"; \
	fi; \
	if [ -e "$${KERNELSRC}/.config" ] && grep -q '^CONFIG_SMP=y$$' "$${KERNELSRC}/.config"; then \
		MODS_DIR="$${MODS_DIR}$${SMPSUFFIX}" ; \
	fi; \
	( \
		if cd modules; then \
			make --quiet --no-print-directory CNXT_KERNELSRC=$${KERNELSRC} CNXT_MODS_DIR=$${MODS_DIR} clean all modules_install || exit $$?; \
		fi \
	) || exit $$?
endif
	$(MAKE) all
	$(MAKE) ROOT=`pwd`/debian/tmp install
ifneq ($(TARGET_KERNEL),)
	@PWD="`pwd`"; \
	rm -fr "$${PWD}/debian/tmp/@CNXTLIBDIR@/modules/include" "$${PWD}/debian/tmp/@CNXTLIBDIR@/modules/imported"; \
	find "$${PWD}/debian/tmp/@CNXTLIBDIR@/modules" -maxdepth 3 ! -name "COPYING" ! -name "*o" -type f -exec rm -f {} \;
endif
	rm -rf debian/@CNXTTARGET@modem-doc
ifneq ($(CNXTDRIVER),dgc)
	echo "./usr/share/doc/@CNXTTARGET@modem/100498D_RM_HxF_Released.pdf" > debian/@<EMAIL>
endif
	# remove duplicate LICENSE
	rm -f debian/tmp/usr/lib/@CNXTTARGET@modem/LICENSE
ifeq ($(CNXTDRIVER),dgc)
	dh_installdocs BUGS CHANGES INSTALL LICENSE README
else
	dh_installdocs BUGS CHANGES CREDITS FAQ INSTALL LICENSE README # 100498D_RM_HxF_Released.pdf
endif
	dh_installchangelogs
	dh_installdeb
	dh_compress
	dh_md5sums
	dpkg-gencontrol -p@CNXTTARGET@modem -isp
	chown -R root.root debian/tmp
#	chmod -R go=rX debian/tmp
	dpkg --build debian/tmp ..

define checkdir
	test -f debian/rules
endef

binary: binary-indep binary-arch

checkroot:
	$(checkdir)
	test root = "`whoami`"

.PHONY: binary binary-arch binary-indep clean checkroot
