/*
 * Copyright (c) 2003-2010 Linuxant inc.
 * Copyright (c) 2001-2010 Conexant Systems, Inc.
 * 
 * 1.   Permitted use. Redistribution and use in source and binary forms,
 * without modification, are only permitted under the terms set forth herein.
 * 
 * 2.   Disclaimer of Warranties. LINUXANT, ITS SUPPLIERS, AND OTHER CONTRIBUTORS
 * MAKE NO REPRESENTATION ABOUT THE SUITABILITY OF THIS SOFTWARE FOR ANY PURPOSE.
 * IT IS PROVIDED "AS IS" WITHOUT EXPRESS OR IMPLIED WARRANTIES OF ANY KIND.
 * LINUXANT AND OTHER CONTRIBUTORS DISCLAIMS ALL WARRANTIES WITH REGARD
 * TO THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE, GOOD TITLE AND AGAINST INFRINGEMENT.
 * 
 * This software has not been formally tested, and there is no guarantee that
 * it is free of errors including, but not limited to, bugs, defects,
 * interrupted operation, or unexpected results. Any use of this software is
 * at user's own risk.
 * 
 * 3.   No Liability.
 * 
 * (a) Linuxant, its suppliers, or contributors shall not be responsible for
 * any loss or damage to users, customers, or any third parties for any reason
 * whatsoever, and LINUXANT, ITS SUPPLIERS OR CONTRIBUTORS SHALL NOT BE LIABLE
 * FOR ANY ACTUAL, DIRECT, INDIRECT, SPECIAL, PUNITIVE, INCIDENTAL, OR
 * CONSEQUENTIAL (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED, WHETHER IN CONTRACT, STRICT OR OTHER LEGAL THEORY OF
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
 * WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
 * OF SUCH DAMAGE.
 * 
 * (b) User agrees to hold Linuxant, its suppliers, and contributors harmless
 * from any liability, loss, cost, damage or expense, including attorney's fees,
 * as a result of any claims which may be made by any person, including
 * but not limited to User, its agents and employees, its customers, or
 * any third parties that arise out of or result from the manufacture,
 * delivery, actual or alleged ownership, performance, use, operation
 * or possession of the software furnished hereunder, whether such claims
 * are based on negligence, breach of contract, absolute liability or any
 * other legal theory.
 * 
 * 4.   Notices. User hereby agrees not to remove, alter or destroy any
 * copyright, trademark, credits, other proprietary notices or confidential
 * legends placed upon, contained within or associated with the Software,
 * and shall include all such unaltered copyright, trademark, credits,
 * other proprietary notices or confidential legends on or in every copy of
 * the Software.
 * 
 * 5.   Reverse-engineering. User hereby agrees not to reverse engineer,
 * decompile, or disassemble the portions of this software provided solely
 * in object form, nor attempt in any manner to obtain their source-code.
 * 
 * 6.   Redistribution. Redistribution of this software is only permitted
 * for exact copies (without modification) of versions explicitly marked
 * and officially released by Linuxant with the word "free" in their name.
 * Redistribution or disclosure of other versions, derivatives or license key
 * information is expressly prohibited without explicit written approval signed
 * by an authorized Linuxant officer.
 * 
 * 7.   Performance. V.92 modems are designed to be capable of receiving data at
 * up to 56Kbps with compatible phone line and server equipment, and transmitting
 * data at up to 31.2Kbps. V.90 modems are designed to be capable of receiving
 * data at up to 56 Kbps from a compatible service provider and transmitting data
 * at up to about 28.8 Kbps. Public networks currently limit download speeds to
 * about 53Kbps. The free version of the drivers is limited to 14.4Kbps.
 * Actual speeds vary and are often less than the maximum possible.
 * 
 * 
 */

		/* WARNING: THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT MANUALLY! */
		#ifndef __OSUNIQREDEF_H__
		#define __OSUNIQREDEF_H__

		#define REAL_CONCAT(a, b, c, d) a##b##c##d
		#define CONCAT(a, b, c, d) REAL_CONCAT(a, b, c, d)

		/* prevent potential naming collisions with other modules. */
		#define _OSUNIQDEF_PREFIX_STR "cnxthsf_"CNXTLINUXVERSION_NODOT"_"
		#define _OSUNIQDEF(f) CONCAT(cnxthsf_, CNXTLINUXVERSION_NODOT, _, f)
		#define _OSUNIQDEF_STR(f) _OSUNIQDEF_PREFIX_STR#f

	
#define ComCtrl_Create _OSUNIQDEF(ComCtrl_Create)
#define ComCtrl_Destroy _OSUNIQDEF(ComCtrl_Destroy)
#define ComCtrl_Open _OSUNIQDEF(ComCtrl_Open)
#define ComCtrl_Close _OSUNIQDEF(ComCtrl_Close)
#define ComCtrl_Configure _OSUNIQDEF(ComCtrl_Configure)
#define ComCtrl_Monitor _OSUNIQDEF(ComCtrl_Monitor)
#define ComCtrl_Control _OSUNIQDEF(ComCtrl_Control)
#define ComCtrl_Read _OSUNIQDEF(ComCtrl_Read)
#define ComCtrl_Write _OSUNIQDEF(ComCtrl_Write)
#define OsAllocate _OSUNIQDEF(OsAllocate)
#define OsFree _OSUNIQDEF(OsFree)
#define OsAtomicAdd _OSUNIQDEF(OsAtomicAdd)
#define OsAtomicCompareAndSwapEx _OSUNIQDEF(OsAtomicCompareAndSwapEx)
#define OsAtomicDecrement _OSUNIQDEF(OsAtomicDecrement)
#define OsAtomicIncrement _OSUNIQDEF(OsAtomicIncrement)
#define OsCreatePeriodicTimer _OSUNIQDEF(OsCreatePeriodicTimer)
#define OsDestroyPeriodicTimer _OSUNIQDEF(OsDestroyPeriodicTimer)
#define OsSetPeriodicTimer _OSUNIQDEF(OsSetPeriodicTimer)
#define OsImmediateTimeOut _OSUNIQDEF(OsImmediateTimeOut)
#define OsGetCurrentThread _OSUNIQDEF(OsGetCurrentThread)
#define OsGetSystemTime _OSUNIQDEF(OsGetSystemTime)
#define OsCriticalSectionCreate _OSUNIQDEF(OsCriticalSectionCreate)
#define OsCriticalSectionDestroy _OSUNIQDEF(OsCriticalSectionDestroy)
#define OsCriticalSectionAcquire _OSUNIQDEF(OsCriticalSectionAcquire)
#define OsCriticalSectionRelease _OSUNIQDEF(OsCriticalSectionRelease)
#define OsLockCreate _OSUNIQDEF(OsLockCreate)
#define OsLockDestroy _OSUNIQDEF(OsLockDestroy)
#define OsLockLock _OSUNIQDEF(OsLockLock)
#define OsLockUnlock _OSUNIQDEF(OsLockUnlock)
#define OsLockTry _OSUNIQDEF(OsLockTry)
#define OsLockTryUnlock _OSUNIQDEF(OsLockTryUnlock)
#define OsSleep _OSUNIQDEF(OsSleep)
#define OsStrCpy _OSUNIQDEF(OsStrCpy)
#define OsStrnCpy _OSUNIQDEF(OsStrnCpy)
#define OsStrCat _OSUNIQDEF(OsStrCat)
#define OsStrnCat _OSUNIQDEF(OsStrnCat)
#define OsStrCmp _OSUNIQDEF(OsStrCmp)
#define OsStrnCmp _OSUNIQDEF(OsStrnCmp)
#define OsToupper _OSUNIQDEF(OsToupper)
#define OsTolower _OSUNIQDEF(OsTolower)
#define OsIsDigit _OSUNIQDEF(OsIsDigit)
#define OsSprintf _OSUNIQDEF(OsSprintf)
#define OsVSprintf _OSUNIQDEF(OsVSprintf)
#define OsStrLen _OSUNIQDEF(OsStrLen)
#define OsAtoi _OSUNIQDEF(OsAtoi)
#define OsMemSet _OSUNIQDEF(OsMemSet)
#define OsMemCpy _OSUNIQDEF(OsMemCpy)
#define OsMemCmp _OSUNIQDEF(OsMemCmp)
#define OsMemMove _OSUNIQDEF(OsMemMove)
#define OsRawVPrintf _OSUNIQDEF(OsRawVPrintf)
#define OsRawPrintf _OSUNIQDEF(OsRawPrintf)
#define OsRawNLPrintf _OSUNIQDEF(OsRawNLPrintf)
#define OsErrorVPrintf _OSUNIQDEF(OsErrorVPrintf)
#define OsErrorPrintf _OSUNIQDEF(OsErrorPrintf)
#define OsDebugVPrintf _OSUNIQDEF(OsDebugVPrintf)
#define OsDebugPrintf _OSUNIQDEF(OsDebugPrintf)
#define OsDebugBreakpoint _OSUNIQDEF(OsDebugBreakpoint)
#define NVM_Open _OSUNIQDEF(NVM_Open)
#define NVM_Close _OSUNIQDEF(NVM_Close)
#define NVM_Read _OSUNIQDEF(NVM_Read)
#define NVM_Write _OSUNIQDEF(NVM_Write)
#define NVM_WriteFlushList _OSUNIQDEF(NVM_WriteFlushList)
#define OsEventCreate _OSUNIQDEF(OsEventCreate)
#define OsEventDestroy _OSUNIQDEF(OsEventDestroy)
#define OsEventInit _OSUNIQDEF(OsEventInit)
#define OsEventSet _OSUNIQDEF(OsEventSet)
#define OsEventClear _OSUNIQDEF(OsEventClear)
#define OsEventState _OSUNIQDEF(OsEventState)
#define OsEventWait _OSUNIQDEF(OsEventWait)
#define OsEventWaitTime _OSUNIQDEF(OsEventWaitTime)
#define OsKernelUsesRegParm _OSUNIQDEF(OsKernelUsesRegParm)
#define OsDpcCreate _OSUNIQDEF(OsDpcCreate)
#define OsDpcDestroy _OSUNIQDEF(OsDpcDestroy)
#define OsDpcSchedule _OSUNIQDEF(OsDpcSchedule)
#define OsDpcEnable _OSUNIQDEF(OsDpcEnable)
#define OsDpcDisable _OSUNIQDEF(OsDpcDisable)
#define OsUsbAllocateUrbs _OSUNIQDEF(OsUsbAllocateUrbs)
#define OsUsbFWDownload _OSUNIQDEF(OsUsbFWDownload)
#define OsUsbFreeUrbs _OSUNIQDEF(OsUsbFreeUrbs)
#define OsUsbCreateInstance _OSUNIQDEF(OsUsbCreateInstance)
#define OsUsbDestroyInstance _OSUNIQDEF(OsUsbDestroyInstance)
#define OsUsbReturnUnusedRequest _OSUNIQDEF(OsUsbReturnUnusedRequest)
#define OsUsbGetControlRequest _OSUNIQDEF(OsUsbGetControlRequest)
#define OsUsbGetTxRequest _OSUNIQDEF(OsUsbGetTxRequest)
#define OsUsbIsStarted _OSUNIQDEF(OsUsbIsStarted)
#define OsUsbMakeControlRequest _OSUNIQDEF(OsUsbMakeControlRequest)
#define OsUsbMakeControlRequestSync _OSUNIQDEF(OsUsbMakeControlRequestSync)
#define OsUsbMakeDataReceiveRequest _OSUNIQDEF(OsUsbMakeDataReceiveRequest)
#define OsUsbMakeDataTransmitRequest _OSUNIQDEF(OsUsbMakeDataTransmitRequest)
#define OsUsbMakeDownloadTransmitRequest _OSUNIQDEF(OsUsbMakeDownloadTransmitRequest)
#define OsUsbMakeNotifyRequest _OSUNIQDEF(OsUsbMakeNotifyRequest)
#define OsUsbResetPipes _OSUNIQDEF(OsUsbResetPipes)
#define OsUsbResetPort _OSUNIQDEF(OsUsbResetPort)
#define OsUsbWaitControlRequest _OSUNIQDEF(OsUsbWaitControlRequest)
#define OsUsbWaitForControlRequestCompletion _OSUNIQDEF(OsUsbWaitForControlRequestCompletion)
#define OsGetPCIDeviceResources _OSUNIQDEF(OsGetPCIDeviceResources)
#define OsHookInterrupt _OSUNIQDEF(OsHookInterrupt)
#define OsUnhookInterrupt _OSUNIQDEF(OsUnhookInterrupt)
#define OsMapPhysMem _OSUNIQDEF(OsMapPhysMem)
#define OsUnmapPhysMem _OSUNIQDEF(OsUnmapPhysMem)
#define OsThreadCreate _OSUNIQDEF(OsThreadCreate)
#define OsThreadDestroy _OSUNIQDEF(OsThreadDestroy)
#define OsThreadScheduleInit _OSUNIQDEF(OsThreadScheduleInit)
#define OsThreadSchedule _OSUNIQDEF(OsThreadSchedule)
#define OsThreadScheduleDone _OSUNIQDEF(OsThreadScheduleDone)
#define OsMdmThread _OSUNIQDEF(OsMdmThread)
#define OsGetProcessorFreq _OSUNIQDEF(OsGetProcessorFreq)
#define OsReadCpuCnt _OSUNIQDEF(OsReadCpuCnt)
#define OsCreateTimer _OSUNIQDEF(OsCreateTimer)
#define OsSetTimer _OSUNIQDEF(OsSetTimer)
#define OsCancelTimer _OSUNIQDEF(OsCancelTimer)
#define OsChangeTimerTimeOut _OSUNIQDEF(OsChangeTimerTimeOut)
#define OsDestroyTimer _OSUNIQDEF(OsDestroyTimer)
#define OsFloatSuffix _OSUNIQDEF(OsFloatSuffix)
#define OsFloatPrefix _OSUNIQDEF(OsFloatPrefix)
#define OsMemDMAAllocate _OSUNIQDEF(OsMemDMAAllocate)
#define OsMemDMAFree _OSUNIQDEF(OsMemDMAFree)
#define OsIoMemReadb _OSUNIQDEF(OsIoMemReadb)
#define OsIoMemReadw _OSUNIQDEF(OsIoMemReadw)
#define OsIoMemReadl _OSUNIQDEF(OsIoMemReadl)
#define OsIoMemWriteb _OSUNIQDEF(OsIoMemWriteb)
#define OsIoMemWritew _OSUNIQDEF(OsIoMemWritew)
#define OsIoMemWritel _OSUNIQDEF(OsIoMemWritel)
#define OsIoPortReadb _OSUNIQDEF(OsIoPortReadb)
#define OsIoPortReadw _OSUNIQDEF(OsIoPortReadw)
#define OsIoPortReadl _OSUNIQDEF(OsIoPortReadl)
#define OsIoPortWriteb _OSUNIQDEF(OsIoPortWriteb)
#define OsIoPortWritew _OSUNIQDEF(OsIoPortWritew)
#define OsIoPortWritel _OSUNIQDEF(OsIoPortWritel)
#define OsPciReadConfigdw _OSUNIQDEF(OsPciReadConfigdw)
#define OsPciReadConfigw _OSUNIQDEF(OsPciReadConfigw)
#define OsPciReadConfigb _OSUNIQDEF(OsPciReadConfigb)
#define OsPciWriteConfigdw _OSUNIQDEF(OsPciWriteConfigdw)
#define OsPciWriteConfigw _OSUNIQDEF(OsPciWriteConfigw)
#define OsPciWriteConfigb _OSUNIQDEF(OsPciWriteConfigb)
#define C_ScrFormat _OSUNIQDEF(C_ScrFormat)
#define LinuxScrSetEntryPointCallback _OSUNIQDEF(LinuxScrSetEntryPointCallback)
#define DBGSRV_Print _OSUNIQDEF(DBGSRV_Print)
#define DMPSRV_Dispatcher _OSUNIQDEF(DMPSRV_Dispatcher)
#define DcpCreate _OSUNIQDEF(DcpCreate)
#define DcpDestroy _OSUNIQDEF(DcpDestroy)
#define DcpSetVolume _OSUNIQDEF(DcpSetVolume)
#define DcpCallback _OSUNIQDEF(DcpCallback)
#define OsDcpEnsureDaemonIsRunning _OSUNIQDEF(OsDcpEnsureDaemonIsRunning)
#define OsDiagMgrOpen _OSUNIQDEF(OsDiagMgrOpen)
#define OsDiagMgrClose _OSUNIQDEF(OsDiagMgrClose)
#define OsDiagMgrNotify _OSUNIQDEF(OsDiagMgrNotify)
#define GetIOClientFromId _OSUNIQDEF(GetIOClientFromId)
#define RPT_Init _OSUNIQDEF(RPT_Init)
#define RPT_Read _OSUNIQDEF(RPT_Read)
#define RPT_ScrRead _OSUNIQDEF(RPT_ScrRead)
#define RPT_ScrWrite _OSUNIQDEF(RPT_ScrWrite)
#define RPT_SetMask _OSUNIQDEF(RPT_SetMask)
#define RPT_Shutdown _OSUNIQDEF(RPT_Shutdown)
#define RPT_Write _OSUNIQDEF(RPT_Write)
#define ulTraceMask _OSUNIQDEF(ulTraceMask)
#define ulLogThread _OSUNIQDEF(ulLogThread)
#define GetSOARLibInterface _OSUNIQDEF(GetSOARLibInterface)
#define cnxt_serial_add _OSUNIQDEF(cnxt_serial_add)
#define cnxt_serial_remove _OSUNIQDEF(cnxt_serial_remove)

#endif /* __OSUNIQREDEF_H__ */

