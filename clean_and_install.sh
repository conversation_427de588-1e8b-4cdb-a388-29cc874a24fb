#!/bin/bash

echo "Cleaning and installing HSF modem driver..."

# Clean any previous build artifacts
echo "Cleaning previous build..."
make clean

# Remove any existing generated files that might conflict
echo "Removing conflicting files..."
rm -f scripts/hsfconfig.in
rm -f scripts/cnxtconfig
rm -f scripts/hsfconfig
rm -f scripts/hsfdcpd
rm -f scripts/hsfstop
rm -f scripts/rchsf
rm -f hsfmodem.spec

# Clean NVM directory
cd nvm
make clean 2>/dev/null || true
rm -f cvt/*
cd ..

# Clean modules
cd modules
make clean 2>/dev/null || true
cd ..

echo "Clean completed. Now attempting installation..."

# Try installation
sudo make install

if [ $? -eq 0 ]; then
    echo "Installation successful!"
    echo "You can now run: sudo hsfconfig"
else
    echo "Installation failed. Check the error messages above."
    exit 1
fi
