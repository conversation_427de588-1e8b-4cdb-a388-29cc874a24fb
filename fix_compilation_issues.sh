#!/bin/bash

echo "Fixing compilation issues for modern kernels..."

# Backup original files
cp modules/GPL/oscompat.h modules/GPL/oscompat.h.backup
cp modules/Makefile modules/Makefile.backup

echo "1. Fixing LINUX_VERSION_CODE and KERNEL_VERSION definitions..."

# Fix the version code issue in oscompat.h
cat > /tmp/version_fix.patch << 'EOF'
--- a/modules/GPL/oscompat.h
+++ b/modules/GPL/oscompat.h
@@ -69,6 +69,13 @@
 #include "osuniqredef.h"
 
 #include <linux/version.h>
+
+/* Ensure LINUX_VERSION_CODE and KERNEL_VERSION are defined */
+#ifndef LINUX_VERSION_CODE
+#include <linux/version.h>
+#endif
+#ifndef KERNEL_VERSION
+#define KERNEL_VERSION(a,b,c) (((a) << 16) + ((b) << 8) + (c))
+#endif
 #ifdef FOUND_LINUX_CONFIG
 #include <linux/config.h>
 #endif
EOF

echo "2. Fixing asm/system.h issue..."

# Fix asm/system.h issue
sed -i 's/#include <asm\/system\.h>/\/* asm\/system.h was removed in Linux 3.4, functionality moved to other headers *\/\n#if LINUX_VERSION_CODE < KERNEL_VERSION(3,4,0)\n#include <asm\/system.h>\n#else\n#include <asm\/switch_to.h>\n#include <asm\/barrier.h>\n#endif/' modules/GPL/oscompat.h

echo "3. Fixing SUBDIRS issue..."

# Fix SUBDIRS issue
sed -i 's/SUBDIRS+=/M=/g' modules/Makefile

echo "4. Adding version definitions at the top of oscompat.h..."

# Add version definitions at the very beginning after includes
sed -i '/^#include <linux\/version\.h>/a\\n/* Ensure LINUX_VERSION_CODE and KERNEL_VERSION are defined */\n#ifndef LINUX_VERSION_CODE\n#include <linux/version.h>\n#endif\n#ifndef KERNEL_VERSION\n#define KERNEL_VERSION(a,b,c) (((a) << 16) + ((b) << 8) + (c))\n#endif' modules/GPL/oscompat.h

echo "Fixes applied. Now try building again with:"
echo "sudo hsfconfig"
