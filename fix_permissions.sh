#!/bin/bash

echo "Fixing file permissions for HSF modem driver..."

# Fix permissions on all scripts and executables
find . -type f \( -name "*.pl" -o -name "*.sh" -o -name "*config*" -o -name "linksame" -o -name "patcher" \) -exec chmod +x {} \;

# Specifically fix the problematic files
chmod +x nvm/cvtinf.pl
chmod +x nvm/linksame
chmod +x scripts/hsfconfig
chmod +x scripts/cnxtconfig.in
chmod +x modules/kernelcompiler.sh
chmod +x test_compile.sh

echo "Permissions fixed. You can now run 'sudo make install'"
